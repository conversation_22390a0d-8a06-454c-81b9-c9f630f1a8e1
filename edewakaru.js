// ==UserScript==
// @name         「絵でわかる日本語」閲覧体験強化
// @namespace    http://tampermonkey.net/
// @version      2025-07-08
// @description  「絵でわかる日本語」サイトの漢字の読み方を括弧書きからルビ表示に自動変換し、広告や不要な要素を非表示にすることで、快適な読書環境を提供します。設定パネルからルビの表示・非表示も簡単に切り替え可能です。
// @icon         https://livedoor.blogimg.jp/edewakaru/imgs/8/c/8cdb7924.png
// <AUTHOR>
// @match        https://www.edewakaru.com/*
// @grant        GM_addStyle
// @grant        GM_getValue
// @grant        GM_setValue
// @run-at       document-start
// ==/UserScript==

;(function () {
  ;('use strict')

  // ========================================
  // 特例配置区域
  // ========================================

  // 复合词列表（支持多种格式）
  const RAW_COMPOUND_WORDS = [
    // 标准格式：汉字（注音）
    '長い間（ながいあいだ）',
    '座り心地（すわりごこち）',
    '触り心地（さわりごこち）',
    '申し訳（もうしわけ）',
    '出張（しゅっちょう）',
    '大好き（だいすき）',
    '唐揚げ（からあげ）',
    '立ち読み（たちよみ）',
    '１杯（いっぱい）',
    '１回（いっかい）',
    '１泊（いっぱく）',
    '１か月（いっかげつ）',
    '１か月間（いっかげつかん）',
    '試験（しけん）',
    '使用（しよう）',
    '前（まえ）',
    '待（ま）',
    '日記（にっき）',
    '話し手（はなして）',
    '聞き手（ききて）',
    '以上（いじょう）',
    '使い方（つかいかた）',
    '０点（れいてん）',
    '買い物（かいもの）',
    '動作（どうさ）',
    'm（メートル）',
    '味覚 （みかく）',
    '気持ち（きもち）',
    '青い色（あおいいろ）',
    '吐き気（はきけ）',
    '元カレ（もとかれ）',
    '髪の毛（かみのけ）',
    '駅（えき）',
    '万引き（まんびき）',
    '通（どお）',
    '遅刻（ちこく）',
    '経（た）',
    '三分の一（さんぶんのいち）',
    '折があれば（おりがあれば）',
    '折を見て（おりをみて）',
    '折に触れて（おりにふれて）',
    '折も折（おりもおり）',
    '残業（ざんぎょう）',
    '合（あ）',
    '楽（たの）',
    '貸し借り（かしかり）',
    '入学（にゅうがく）',
    '暮（ぐ）',
    '届け出（とどけで）',
    '有名（ゆうめい）',
    '自身（じしん）',
    '住（す）',
    '夕ご飯（ゆうごはん）',
    '星の数（ほしのかず）',
    '窓の外（まどのそと）',
    '考え方（かんがえかた）',
    '感じ方（かんじかた）',
    //'付き合（つきあい）',
    '貯（た）',
    '悩み事（なやみごと）',
    //'寄り道（よりみち）',
    '歩（ある）',
    //'泣きっ面に蜂（なきっつらにはち）',
    '食べず嫌い（たべずぎらい）',
    'アタック（attack）',
    'お茶する（おちゃする）',
    '入（はい）',
    '使い分け（つかいわけ）',
    '行き渡る（いきわたる）',
    '星の数ほどある（ほしのかずほどある）',
    '星の数ほどいる（ほしのかずほどいる）',
    '５日間（いつかかん）',
    '食べ物（たべもの）',
    'お団子（おだんご）',
    '足が早い（あしがはやい）',

    //'目の色が変わる（めのいろがかわる）',
    //'目の色を変える（めのいろをかえる）',

    // 强制注音
    { pattern: '羽根を伸ばす（羽根を伸ばす）', reading: 'はねをのばす' },
    { pattern: '長蛇の列（長蛇の列）', reading: 'ちょうだのれつ' },
    { pattern: '付き合（つきあい）', reading: 'つきあ' },

    // 强制替换
    { pattern: '目に余る②（めにあまる）', replacement: '<ruby>目<rt>め</rt></ruby>に<ruby>余<rt>あま</rt></ruby>る②' },
    { pattern: '言い方（いいかた）', replacement: '<ruby>言<rt>い</rt></ruby>い<ruby>方<rt>かた</rt></ruby>' },
    { pattern: '言い訳（いいわけ）', replacement: '<ruby>言<rt>い</rt></ruby>い<ruby>訳<rt>わけ</rt></ruby>' },
    {
      pattern: '目の色が変わる・目の色を変える（めのいろがかわる・かえる）',
      replacement:
        '<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>が<ruby>変<rt>かわ</rt></ruby>る・<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>を<ruby>変<rt>かえ</rt></ruby>える',
    },
    {
      pattern: '水の泡になる・水の泡となる（みずのあわになる）',
      replacement: '<ruby>水<rt>みず</rt></ruby>の<ruby>泡<rt>あわ</rt></ruby>になる・<ruby>水<rt>みず</rt></ruby>の<ruby>泡<rt>あわ</rt></ruby>となる',
    },
    {
      pattern: '意味で（いみ）',
      replacement: '<ruby>意味<rt>いみ</rt></ruby>で',
    },
    {
      pattern: '和製英語で（わせいえいご）',
      replacement: '<ruby>和製英語<rt>わせいえいご</rt></ruby>で',
    },
  ]

  // HTML级别的替换规则
  const HTML_REPLACEMENT_RULES = [
    { pattern: /一瞬（いっしゅん<br>）/g, replacement: '<ruby>一瞬<rt>いっしゅん</rt></ruby>' },
    {
      pattern: /<b><span style="font-size: 125%;">居<\/span><\/b>（い）/g,
      replacement: '<b><ruby>居<rt>い</rt></ruby></b>',
    },
    {
      pattern: /<b style="font-size: large;">留守<\/b>（るす）/g,
      replacement: '<b><ruby>留守<rt>るす</rt></ruby></b>',
    },
  ]

  // 始终不转换的注音模式
  const ALWAYS_EXCLUDE = new Set(['挙句（に）', '道草（を）', '以上（は）', '人称（私）', '人称（あなた）', '矢先（に）'])

  // 助词排除列表，且前面非汉字。适用于 'あげく（に）' 不转换
  const RUBY_EXCLUDE_PARTICLES = new Set(['に', 'は', 'を', 'が', 'の', 'と', 'で', 'から', 'まで', 'へ', 'も', 'や', 'ね', 'よ', 'さ'])

  // ========================================
  // 系统常量与正则表达式
  // ========================================

  // 正则表达式 - 预编译优化
  const REGEX_PATTERNS = {
    // 注音格式匹配
    ruby: /([一-龯々]+)（([^（）]*)）/g,
    katakana: /([\u30A0-\u30FF]+)[（(]([\w\s+]+)[）)]/g,
    bracket: /[【「](?:.*?)([^【】「」（）・、\s]+)（([^（）]*)）([^【】「」（）]*)[】」]/g,

    // 字符类型检测 - 预编译测试函数
    kanaOnly: /^[\u3040-\u309F]+$/,
    nonKana: /[^\u3040-\u309F]/,
    isKanaChar: /^[\u3040-\u309F]$/,

    // 图片链接处理
    imgSrc: /(https:\/\/livedoor\.blogimg\.jp\/edewakaru\/imgs\/[a-z0-9]+\/[a-z0-9]+\/[a-z0-9]+)-s(\.jpg)/i,
  }

  // 正则表达式测试函数缓存 - 避免重复编译和提高性能
  const REGEX_TESTERS = {
    isKanaOnly: (text) => REGEX_PATTERNS.kanaOnly.test(text),
    hasNonKana: (text) => REGEX_PATTERNS.nonKana.test(text),
    isKanaChar: (char) => REGEX_PATTERNS.isKanaChar.test(char),
  }

  // DOM 节点类型常量
  const NODE_TYPES = {
    TEXT: Node.TEXT_NODE,
    ELEMENT: Node.ELEMENT_NODE,
  }

  // 空白节点配置
  const WHITESPACE_NODE_CONFIG = {
    // 空白文本正则
    WHITESPACE_REGEX: /^\s*$/,
    // 需要移除的元素标签
    EMPTY_TAGS: new Set(['BR']),
    // 需要移除的特殊内容元素
    SPECIAL_CONTENT: new Map([['SPAN', '&nbsp;']]),
  }

  // 全局移除的选择器
  const GLOBAL_REMOVE_SELECTORS = [
    'header#blog-header',
    'footer#blog-footer',
    '.ldb_menu',
    '.article-social-btn',
    '.adsbygoogle',
    'a[href*="blogmura.com"]',
    'a[href*="with2.net"]',
  ]

  // ========================================
  // 运行时数据结构 - 动态生成
  // ========================================

  // 优化后的数据结构 - 在脚本初始化时预处理
  let PROCESSED_COMPOUND_WORDS = {
    // 按首字符分组的 Map，用于快速查找
    segmentWords: new Map(), // 需要分割处理的词汇：Map <首字符, Array<{pattern, kanji, reading, regex}>>
    replaceWords: new Map(), // 直接替换的词汇：Map <首字符, Array<{pattern, replacement, regex}>>
    // 全局正则表达式，用于一次性匹配所有可能的模式
    globalRegex: null,
    // 模式到预计算结果的映射，避免函数调用开销
    patternResults: new Map(),
  }

  // ========================================
  // 设置项管理
  // ========================================

  // 设置键名常量
  const SETTINGS_KEYS = {
    SCRIPT_ENABLED: 'ruby_converter_enabled',
    FURIGANA_VISIBLE: 'furigana_visible',
  }
  // 设置配置
  const SETTINGS_CONFIG = [
    {
      key: SETTINGS_KEYS.SCRIPT_ENABLED,
      label: 'ページ最適化',
      defaultValue: true,
      description: 'ページの最適化とコンテンツクリーニング機能を有効にします',
      handler: handleScriptToggle,
    },
    {
      key: SETTINGS_KEYS.FURIGANA_VISIBLE,
      label: '振り仮名表示',
      defaultValue: true,
      description: '振り仮名の表示・非表示を切り替えます',
      handler: handleFuriganaToggle,
    },
  ]

  // 从存储中获取设置值
  function getSettingValue(key, defaultValue) {
    return GM_getValue(key, defaultValue)
  }

  // 将设置值保存到存储中
  function setSettingValue(key, value) {
    GM_setValue(key, value)
  }

  // 处理主脚本功能的开启
  function handleScriptToggle(enabled) {
    setSettingValue(SETTINGS_KEYS.SCRIPT_ENABLED, enabled)
    showNotification('設定を保存しました。ページを再読み込みしてください。')
  }

  // 处理振假名显示切换
  function handleFuriganaToggle(visible) {
    setSettingValue(SETTINGS_KEYS.FURIGANA_VISIBLE, visible)
    toggleFuriganaDisplay(visible)
  }

  // 切换振假名显示状态，通过修改 CSS 样式来控制所有 rt 元素的显示/隐藏
  function toggleFuriganaDisplay(visible) {
    const styleId = 'furigana-display-style'
    let styleElement = document.getElementById(styleId)
    if (!styleElement) {
      styleElement = document.createElement('style')
      styleElement.id = styleId
      document.head.appendChild(styleElement)
    }
    const displayValue = visible ? 'ruby-text' : 'none'
    styleElement.textContent = `rt { display: ${displayValue} !important; }`
  }

  // 显示通知消息
  function showNotification(message) {
    const notification = document.createElement('div')
    notification.className = 'settings-notification'
    notification.textContent = message
    document.body.appendChild(notification)
    setTimeout(() => notification.remove(), 2000)
  }

  // ========================================
  // 工具函数组 - 基础辅助函数
  // ========================================

  // 正则表达式特殊字符转义函数
  function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  /**
   * 预处理复合词数据，将原始的混合格式复合词数据转换为优化的数据结构，预计算所有处理结果以避免运行时函数调用开销
   */
  function preprocessCompoundWords(rawWords) {
    const segmentWords = new Map()
    const replaceWords = new Map()
    const allPatterns = []
    const patternResults = new Map()
    rawWords.forEach((entry) => {
      const parsed = parseCompoundEntry(entry)
      if (!parsed) return
      const firstChar = parsed.pattern[0]
      const escapedPattern = escapeRegExp(parsed.pattern)
      const regex = new RegExp(escapedPattern, 'g')
      if (parsed.replacement) {
        // 直接替换类型
        if (!replaceWords.has(firstChar)) {
          replaceWords.set(firstChar, [])
        }
        const wordData = {
          pattern: parsed.pattern,
          replacement: parsed.replacement,
          regex: regex,
        }
        replaceWords.get(firstChar).push(wordData)
        // 直接存储结果，避免函数调用
        patternResults.set(parsed.pattern, parsed.replacement)
      } else if (parsed.kanji && parsed.reading) {
        // 分割处理类型
        if (!segmentWords.has(firstChar)) {
          segmentWords.set(firstChar, [])
        }
        const wordData = {
          pattern: parsed.pattern,
          kanji: parsed.kanji,
          reading: parsed.reading,
          regex: regex,
        }
        segmentWords.get(firstChar).push(wordData)
        // 预计算分割结果，避免运行时计算
        const precomputedResult = segmentCompoundWord(parsed.kanji, parsed.reading)
        patternResults.set(parsed.pattern, precomputedResult)
      }
      allPatterns.push(escapedPattern)
    })
    // 创建全局正则表达式，用于一次性匹配所有模式
    const globalRegex = allPatterns.length > 0 ? new RegExp(`(${allPatterns.join('|')})`, 'g') : null
    return {
      segmentWords,
      replaceWords,
      globalRegex,
      patternResults,
    }
  }

  // ========================================
  // 文本处理函数组 - 核心业务逻辑
  // ========================================

  /**
   * 解析不同格式的复合词条目，支持字符串格式和对象格式
   * @param {string|object} entry - 复合词条目，可以是字符串或对象
   * @returns {object|null} 解析后的对象，包含 pattern、kanji、reading 或 replacement 属性
   */
  function parseCompoundEntry(entry) {
    // 字符串格式："汉字（注音）"
    if (typeof entry === 'string') {
      const leftIdx = entry.indexOf('（')
      const rightIdx = entry.lastIndexOf('）')
      if (leftIdx > 0 && rightIdx > leftIdx) {
        return {
          pattern: entry,
          kanji: entry.slice(0, leftIdx),
          reading: entry.slice(leftIdx + 1, rightIdx),
        }
      }
    }
    // 对象格式：{ pattern: '...', reading: '...' }
    else if (entry && entry.reading) {
      return {
        pattern: entry.pattern,
        kanji: entry.pattern.replace(/（.*?）/, ''),
        reading: entry.reading,
      }
    }
    // 对象格式：{ pattern: '...', replacement: '...' }
    else if (entry && entry.replacement) {
      return entry
    }
    return null
  }

  /**
   * 分割复合词并生成 Ruby 标签
   * 将复合词的汉字和读音进行智能分割，生成对应的 Ruby 标签，使用直接字符串拼接避免数组开销
   * @param {string} kanji - 汉字部分
   * @param {string} reading - 读音部分
   * @returns {string} 生成的 HTML Ruby 标签字符串
   */
  function segmentCompoundWord(kanji, reading) {
    // 使用直接字符串拼接，避免数组创建和 join 操作
    let result = ''
    let kanjiIndex = 0
    let readingIndex = 0
    while (kanjiIndex < kanji.length) {
      // 检查当前字符是否为平假名 - 使用优化的正则表达式
      if (REGEX_TESTERS.isKanaChar(kanji[kanjiIndex])) {
        // 如果是平假名，直接添加到结果中
        result += kanji[kanjiIndex]
        kanjiIndex++
        // 在注音中找到对应的平假名位置
        readingIndex = reading.indexOf(kanji[kanjiIndex - 1], readingIndex) + 1
      } else {
        // 处理连续的汉字部分
        let kanjiPart = ''
        let readingPart = ''
        // 收集连续的汉字
        while (kanjiIndex < kanji.length && !REGEX_TESTERS.isKanaChar(kanji[kanjiIndex])) {
          kanjiPart += kanji[kanjiIndex]
          kanjiIndex++
        }
        // 确定对应的注音部分
        const nextKanaIndex = kanjiIndex < kanji.length ? reading.indexOf(kanji[kanjiIndex], readingIndex) : reading.length
        readingPart = reading.substring(readingIndex, nextKanaIndex)
        readingIndex = nextKanaIndex
        // 直接拼接 ruby 标签，避免创建临时数组元素
        result += `<ruby>${kanjiPart}<rt>${readingPart}</rt></ruby>`
      }
    }
    return result
  }

  /**
   * 将文本中的注音格式转换为 Ruby 标签，使用预计算结果避免函数调用开销
   * @param {string} text - 需要处理的文本内容
   * @returns {string} 处理后的文本内容，包含 Ruby 标签
   */
  function processTextContent(text) {
    // 提前检查文本是否包含可能的注音格式，避免不必要的处理
    if (!text.includes('（') && !text.includes('(')) {
      return text
    }
    // 使用全局正则表达式一次性匹配所有复合词模式，直接获取预计算结果
    if (PROCESSED_COMPOUND_WORDS.globalRegex) {
      text = text.replace(PROCESSED_COMPOUND_WORDS.globalRegex, (match) => {
        // 直接从 Map 获取预计算结果，避免函数调用开销
        const result = PROCESSED_COMPOUND_WORDS.patternResults.get(match)
        return result || match
      })
    }
    // 处理常规注音
    // 片假名+英文注音处理（支持全角/半角括号）
    text = text.replace(REGEX_PATTERNS.katakana, (_, katakana, romaji) => {
      // 直接整体作为注音，不拆分 +
      return `<ruby>${katakana}<rt>${romaji}</rt></ruby>`
    })
    // 汉字（平假名）注音处理
    return text.replace(REGEX_PATTERNS.ruby, (_, kanji, reading) => {
      const fullMatch = kanji + '（' + reading + '）'
      // 检查是否在始终不转换列表中
      if (ALWAYS_EXCLUDE.has(fullMatch)) {
        return _
      }
      // 排除助词且 kanji 全为平假名 - 使用优化的正则表达式测试
      if (RUBY_EXCLUDE_PARTICLES.has(reading) && REGEX_TESTERS.isKanaOnly(kanji)) return _
      // 排除非假名注音
      if (REGEX_TESTERS.hasNonKana(reading)) return _
      return reading ? `<ruby>${kanji}<rt>${reading}</rt></ruby>` : _
    })
  }

  // ========================================
  // DOM操作函数组 - DOM相关操作
  // ========================================

  /**
   * 判断一个节点是否为"空白节点"（如空文本、<br>、&nbsp;等）
   * @param {Node} node - 需要检查的节点
   * @returns {boolean}
   */
  function isWhitespaceNode(node) {
    // 节点不存在，直接返回false
    if (!node) {
      return false
    }

    // 检查是否为空白文本节点
    if (node.nodeType === NODE_TYPES.TEXT && WHITESPACE_NODE_CONFIG.WHITESPACE_REGEX.test(node.textContent)) {
      return true
    }

    // 检查是否为需要移除的元素节点
    if (node.nodeType === NODE_TYPES.ELEMENT) {
      const tagName = node.tagName

      // 检查是否为空标签（如 <br>）
      if (WHITESPACE_NODE_CONFIG.EMPTY_TAGS.has(tagName)) {
        return true
      }

      // 检查是否为特殊内容元素（如 <span>&nbsp;</span>）
      const expectedContent = WHITESPACE_NODE_CONFIG.SPECIAL_CONTENT.get(tagName)
      if (expectedContent && node.innerHTML === expectedContent) {
        return true
      }
    }

    return false
  }

  /**
   * 移除容器开头和结尾的空白文本节点、换行符和空格元素（优化版）
   * @param {HTMLElement} container - 需要清理的容器元素
   */
  function trimContainerBreaks(container) {
    // 参数验证
    if (!container || !container.nodeType) {
      return
    }

    // 从开头清理空白节点
    while (isWhitespaceNode(container.firstChild)) {
      container.removeChild(container.firstChild)
    }

    // 从结尾清理空白节点
    while (isWhitespaceNode(container.lastChild)) {
      container.removeChild(container.lastChild)
    }
  }

  /**
   * 将博客图片链接转换为直接的图片元素，并优化图片显示
   * @param {HTMLElement} container - 包含图片链接的容器元素
   */
  function processImageLinks(container) {
    const imageLinks = container.querySelectorAll('a[href*="livedoor.blogimg.jp"]')
    imageLinks.forEach((link) => {
      const img = link.querySelector('img.pict')
      if (!img) return
      // 获取原始图片 URL（移除 -s 后缀）
      const originalSrc = img.src.replace(REGEX_PATTERNS.imgSrc, '$1$2')
      // 创建新的图片元素
      const newImg = document.createElement('img')
      newImg.src = originalSrc
      newImg.alt = (img.alt || '').replace(/blog/gi, '')
      newImg.className = img.className
      newImg.width = img.width
      newImg.height = img.height
      // 替换链接为图片
      link.replaceWith(newImg)
    })
  }

  /**
   * 批量移除页面中不需要的全局元素，如广告、菜单等
   */
  function removeGlobalElements() {
    // 合并选择器，一次查询完成所有元素移除，减少 DOM 遍历次数
    const combinedSelector = GLOBAL_REMOVE_SELECTORS.join(',')
    const elementsToRemove = document.querySelectorAll(combinedSelector)
    // 批量移除元素，减少重排重绘
    elementsToRemove.forEach((el) => el.remove())
  }

  /**
   * 清理文章容器中的不需要元素，包括广告、脚本、不需要的链接等
   * @param {HTMLElement} container - 文章容器元素
   */
  function cleanupContent(container) {
    // 0. 处理图片链接
    processImageLinks(container)
    // 1. 批量收集需要移除的元素，减少 DOM 查询次数
    const elementsToRemove = []
    // 收集不需要的链接
    const unwantedLinks = container.querySelectorAll('a[href*="blogmura.com"], a[href*="with2.net"]')
    elementsToRemove.push(...unwantedLinks)
    // 收集脚本元素
    const scripts = container.querySelectorAll('script')
    elementsToRemove.push(...scripts)
    // 2. 查找 ad2 元素并收集其后续元素
    const adDiv = container.querySelector('#ad2')
    if (adDiv) {
      // 收集 ad2 之后的所有元素
      let nextElement = adDiv.nextElementSibling
      while (nextElement) {
        elementsToRemove.push(nextElement)
        nextElement = nextElement.nextElementSibling
      }
      // 收集 ad2 本身
      elementsToRemove.push(adDiv)
    }
    // 3. 批量移除所有收集的元素，减少重排重绘次数
    elementsToRemove.forEach((el) => el.remove())
    // 4. 清理容器末尾的空白和换行
    trimContainerBreaks(container)
  }

  // 动态词汇管理
  const DYNAMIC_COMPOUND_WORDS = new Set()

  /**
   * 扫描和学习新词条
   * 从页面内容中扫描新的复合词条并动态添加到处理系统中
   * @param {HTMLElement} element - 需要扫描的元素
   */
  function findAndRegisterCompounds(element) {
    if (!element) return
    const htmlContent = element.innerHTML
    let match
    while ((match = REGEX_PATTERNS.bracket.exec(htmlContent)) !== null) {
      const reading = match[2]
      if (reading && !REGEX_TESTERS.hasNonKana(reading)) {
        const compound = match[1] + '（' + match[2] + '）' + match[3]
        // 使用 Set 的 has() 方法，O(1) 时间复杂度
        if (!DYNAMIC_COMPOUND_WORDS.has(compound)) {
          DYNAMIC_COMPOUND_WORDS.add(compound)
          // 动态添加到处理系统中
          addDynamicCompoundWord(compound)
        }
      }
    }
  }

  /**
   * 动态添加复合词到处理系统
   * 将新发现的复合词添加到优化的数据结构中，预计算结果并重建全局正则表达式
   * @param {string} compound - 复合词字符串
   */
  function addDynamicCompoundWord(compound) {
    const parsed = parseCompoundEntry(compound)
    if (!parsed) return
    const firstChar = parsed.pattern[0]
    const escapedPattern = escapeRegExp(parsed.pattern)
    if (parsed.kanji && parsed.reading) {
      if (!PROCESSED_COMPOUND_WORDS.segmentWords.has(firstChar)) {
        PROCESSED_COMPOUND_WORDS.segmentWords.set(firstChar, [])
      }
      const wordData = {
        pattern: parsed.pattern,
        kanji: parsed.kanji,
        reading: parsed.reading,
        regex: new RegExp(escapedPattern, 'g'),
      }
      PROCESSED_COMPOUND_WORDS.segmentWords.get(firstChar).push(wordData)
      // 预计算结果并存储，避免运行时函数调用
      const precomputedResult = segmentCompoundWord(parsed.kanji, parsed.reading)
      PROCESSED_COMPOUND_WORDS.patternResults.set(parsed.pattern, precomputedResult)
      // 重建全局正则表达式以包含新词汇
      rebuildGlobalRegex()
    }
  }

  /**
   * 重建全局正则表达式
   * 重新构建包含所有模式的全局正则表达式
   */
  function rebuildGlobalRegex() {
    const allPatterns = []
    // 收集所有分割处理词汇的模式
    for (const words of PROCESSED_COMPOUND_WORDS.segmentWords.values()) {
      for (const word of words) {
        allPatterns.push(escapeRegExp(word.pattern))
      }
    }
    // 收集所有直接替换词汇的模式
    for (const words of PROCESSED_COMPOUND_WORDS.replaceWords.values()) {
      for (const word of words) {
        allPatterns.push(escapeRegExp(word.pattern))
      }
    }
    // 重建全局正则表达式
    PROCESSED_COMPOUND_WORDS.globalRegex = allPatterns.length > 0 ? new RegExp(`(${allPatterns.join('|')})`, 'g') : null
  }

  /**
   * Ruby 转换处理（DOM 批量化优化版本）
   * 遍历指定根元素下的所有文本节点，使用 DocumentFragment 批量处理 DOM 操作以减少重排重绘
   * @param {HTMLElement} root - 根元素，将处理其下所有文本节点
   */
  function processRubyConversion(root) {
    const treeWalker = document.createTreeWalker(root, NodeFilter.SHOW_TEXT, {
      acceptNode: (node) => (node.parentNode.nodeName !== 'SCRIPT' && node.parentNode.nodeName !== 'STYLE' ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT),
    })
    // 收集需要处理的节点信息
    const nodesToProcess = []
    let node
    while ((node = treeWalker.nextNode())) {
      const newContent = processTextContent(node.nodeValue)
      if (newContent !== node.nodeValue) {
        nodesToProcess.push({ node, newContent })
      }
    }
    // 使用 DocumentFragment 批量处理 DOM 操作，减少重排重绘
    if (nodesToProcess.length > 0) {
      // 按父元素分组，进一步优化 DOM 操作
      const groupedByParent = new Map()
      nodesToProcess.forEach(({ node, newContent }) => {
        const parent = node.parentNode
        if (!groupedByParent.has(parent)) {
          groupedByParent.set(parent, [])
        }
        groupedByParent.get(parent).push({ node, newContent })
      })
      // 对每个父元素进行批量处理
      groupedByParent.forEach((nodeGroup, parent) => {
        // 按节点在 DOM 中的顺序排序，确保正确的替换顺序
        nodeGroup.sort((a, b) => {
          const position = a.node.compareDocumentPosition(b.node)
          return position & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1
        })
        // 从后往前处理，避免位置偏移问题
        for (let i = nodeGroup.length - 1; i >= 0; i--) {
          const { node, newContent } = nodeGroup[i]
          // 使用 DocumentFragment 减少重排重绘
          const fragment = document.createDocumentFragment()
          const tempDiv = document.createElement('div')
          tempDiv.innerHTML = newContent
          // 将解析后的节点移动到 DocumentFragment
          while (tempDiv.firstChild) {
            fragment.appendChild(tempDiv.firstChild)
          }
          // 一次性替换：插入 fragment 并移除原节点
          parent.insertBefore(fragment, node)
          parent.removeChild(node)
        }
      })
    }
  }

  /**
   * 应用 HTML 替换规则
   * 对指定元素应用预定义的 HTML 替换规则
   * @param {HTMLElement} element - 需要应用替换的元素
   * @param {Array} rules - 替换规则数组，每个规则包含 pattern 和 replacement
   */
  function applyHtmlReplacements(element, rules) {
    if (!element || !rules || rules.length === 0) return
    let currentHTML = element.innerHTML
    const originalHTML = currentHTML
    rules.forEach((rule) => {
      currentHTML = currentHTML.replace(rule.pattern, rule.replacement)
    })
    if (currentHTML !== originalHTML) {
      element.innerHTML = currentHTML
    }
  }

  /**
   * 优化侧边栏显示
   * 清理侧边栏内容，只保留分类插件，并设置为可见
   */
  function optimizeSidebar() {
    const sidebar = document.querySelector('aside#sidebar')
    if (!sidebar) return
    const category = sidebar.querySelector('.plugin-categorize')
    sidebar.textContent = ''
    if (category) {
      sidebar.appendChild(category)
      // 显示处理完成的侧边栏
      sidebar.style.visibility = 'visible'
    }
  }

  // ========================================
  // 样式注入函数组 - 样式相关
  // ========================================

  /**
   * 统一设置面板和开关按钮的样式常量
   */
  const UNIFIED_TOGGLE_STYLES = `
    #settings-panel { position: fixed; bottom: 1.3rem; right: 1rem; z-index: 9999; display: flex; flex-direction: column; gap: 0.5rem; padding: 1rem; background: white; border-radius: 4px; box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1),0 4px 6px -2px rgba(0,0,0,0.05); width: 140px; opacity: 0.8; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; }
    .settings-title { font-size: 0.875rem; font-weight: 600; color: #1F2937; margin: 0 0 0.375rem 0; text-align: center; border-bottom: 1px solid #E5E7EB; padding-bottom: 0.375rem; }
    .setting-item { display: flex; align-items: center; justify-content: space-between; gap: 0.5rem; }
    .setting-label { font-size: 0.8125rem; font-weight: 500; color: #4B5563; cursor: pointer; flex: 1; line-height: 1.2; }
    .toggle-switch { position: relative; display: inline-block; width: 2.5rem; height: 1.25rem; flex-shrink: 0; }
    .toggle-switch input { opacity: 0; width: 0; height: 0; }
    .toggle-slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #E5E7EB; transition: all 0.2s ease-in-out; border-radius: 9999px; }
    .toggle-slider:before { position: absolute; content: ""; height: 0.9375rem; width: 0.9375rem; left: 0.15625rem; bottom: 0.15625rem; background-color: white; transition: all 0.2s ease-in-out; border-radius: 50%; box-shadow: 0 1px 3px 0 rgba(0,0,0,0.1),0 1px 2px 0 rgba(0,0,0,0.06); }
    input:checked+.toggle-slider { background-color: #3B82F6; }
    input:checked+.toggle-slider:before { transform: translateX(1.25rem); }
    .toggle-slider:hover { background-color: #D1D5DB; }
    input:checked+.toggle-slider:hover { background-color: #2563EB; }
    .settings-notification { position: fixed; bottom: 9rem; right: 1rem; z-index: 10000; padding: 0.5rem 0.75rem; background-color: #3B82F6; color: white; border-radius: 0.375rem; box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1),0 4px 6px -2px rgba(0,0,0,0.05); font-size: 0.8125rem; font-weight: 500; animation: slideInOut 3s ease-in-out; white-space: nowrap; }
    @keyframes slideInOut { 0% { opacity: 0; transform: translateX(20px); } 15% { opacity: 1; transform: translateX(0); } 85% { opacity: 1; transform: translateX(0); } 100% { opacity: 0; transform: translateX(20px); } }
  `

  /**
   * 初始页面布局和样式优化的样式常量
   */
  const INITIAL_PAGE_STYLES = `
    #container { width: 100%; }
    @media (min-width: 960px) { #container { max-width: 960px; } }
    @media (min-width: 1040px) { #container { max-width: 1040px; } }
    #content { display: flex; position: relative; padding: 50px 0 !important; }
    #main { flex: 1; float: none !important; width: 100% !important; }
    aside#sidebar { visibility: hidden; float: none !important; width: 350px !important; flex: 0 0 350px; }
    .plugin-categorize { position: fixed; height: 85vh; display: flex; flex-direction: column; padding: 0 !important; width: 350px !important; }
    .plugin-categorize .side { flex: 1; overflow-y: auto; max-height: unset; }
    .plugin-categorize .side > :not([hidden]) ~ :not([hidden]) { margin-top: 5px; margin-bottom: 0; }
    .article { padding: 0 0 20px 0 !important; margin-bottom: 30px !important; }
    .article-body { padding: 0 !important; }
    .article-pager { margin-bottom: 0 !important; }
    .article-body-inner { line-height: 2; }
    .article-body-inner img.pict { margin: 0 !important; width: 80% !important; display: block; }
    .article-body-inner strike { color: orange; }
    .article-body-inner iframe { margin: 4px 0 !important; }
    .to-pagetop { position: fixed; bottom: 1.2rem; right: 220px; z-index: 1000; }
    rt, iframe, .pager { -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; }
    header#blog-header, footer#blog-footer, .ldb_menu, .article-social-btn, .adsbygoogle, #ldblog_related_articles_01d4ecf1, #ad2 { display: none !important; }
    .article-body-inner:after, .article-meta:after, #container:after, #content:after, article:after, section:after, .cf:after { content: none !important; display: none !important; height: auto !important; visibility: visible !important; }
  `

  /**
   * 通用CSS注入函数
   * 优先使用 GM_addStyle，如果不可用则创建 <style> 标签注入到页面
   * @param {string} cssText - 要注入的CSS文本字符串
   */
  function injectGlobalCSS(cssText) {
    if (typeof GM_addStyle === 'function') {
      GM_addStyle(cssText)
    } else {
      const style = document.createElement('style')
      style.textContent = cssText
      ;(document.head || document.documentElement).appendChild(style)
    }
  }

  // ========================================
  // 统一控制函数组 - 用户交互控制
  // ========================================

  /**
   * 创建统一设置面板
   * 创建包含所有开关的统一设置面板
   */
  function createUnifiedSettingsPanel() {
    const panel = document.createElement('div')
    panel.id = 'settings-panel'
    // 创建面板标题
    const title = document.createElement('h3')
    title.className = 'settings-title'
    title.textContent = '設定パネル'
    panel.appendChild(title)
    // 为每个设置创建开关
    SETTINGS_CONFIG.forEach((config) => {
      const settingItem = createSettingToggle(config)
      panel.appendChild(settingItem)
    })
    document.body.appendChild(panel)
  }

  /**
   * 创建单个设置开关
   * 根据配置创建单个设置开关元素
   * @param {Object} config - 设置配置对象
   * @returns {HTMLElement} 设置开关元素
   */
  function createSettingToggle(config) {
    const currentValue = getSettingValue(config.key, config.defaultValue)
    const settingId = `setting-${config.key.replace(/_/g, '-')}`
    const container = document.createElement('div')
    container.className = 'setting-item'
    container.innerHTML = `
      <label for="${settingId}" class="setting-label" title="${config.description}">
        ${config.label}
      </label>
      <label class="toggle-switch">
        <input type="checkbox" id="${settingId}" ${currentValue ? 'checked' : ''}>
        <span class="toggle-slider"></span>
      </label>
    `
    // 添加事件监听器
    const checkbox = container.querySelector('input')
    checkbox.addEventListener('change', (e) => {
      config.handler(e.target.checked)
    })
    return container
  }

  /**
   * 初始化振假名显示状态
   * 根据保存的设置初始化振假名的显示状态
   */
  function initializeFuriganaDisplay() {
    const furiganaVisible = getSettingValue(SETTINGS_KEYS.FURIGANA_VISIBLE, true)
    if (!furiganaVisible) {
      // 如果设置为隐藏，则应用隐藏样式
      updateFuriganaGlobalStyle(false)
    }
  }

  // ========================================
  // 主执行函数组 - 程序入口和流程控制
  // ========================================

  // --- 主功能 (仅在开关开启时执行) ---
  if (!getSettingValue(SETTINGS_KEYS.SCRIPT_ENABLED, true)) {
    // 注入开关样式
    injectGlobalCSS(UNIFIED_TOGGLE_STYLES)
    if (document.readyState === 'complete') {
      createUnifiedSettingsPanel()
    } else {
      document.addEventListener('DOMContentLoaded', createUnifiedSettingsPanel)
    }
    return
  }

  // --- 第一步：尽早注入 CSS 防止闪烁 ---
  injectGlobalCSS(UNIFIED_TOGGLE_STYLES)
  injectGlobalCSS(INITIAL_PAGE_STYLES)

  // --- 第二步：预处理复合词数据，优化性能 ---
  PROCESSED_COMPOUND_WORDS = preprocessCompoundWords(RAW_COMPOUND_WORDS)

  /**
   * 主执行函数
   * 脚本的主要执行流程，协调各个功能模块的执行
   */
  function main() {
    // 阶段 1: 立即移除全局元素
    removeGlobalElements()
    // 阶段 2: 当 DOM 内容加载完成后处理主要内容
    const processMainContent = () => {
      const articleBodies = document.querySelectorAll('.article-body-inner')
      // 分批处理文章内容，避免长时间阻塞主线程导致卡顿
      if (articleBodies.length === 0) return
      let currentIndex = 0
      const processBatch = () => {
        const batchSize = Math.min(2, articleBodies.length - currentIndex) // 每批处理最多 2 个元素
        const endIndex = currentIndex + batchSize
        for (let i = currentIndex; i < endIndex; i++) {
          const body = articleBodies[i]
          cleanupContent(body)
          applyHtmlReplacements(body, HTML_REPLACEMENT_RULES)
          findAndRegisterCompounds(body)
          processRubyConversion(body)
          // 显示处理完成的内容
          body.style.opacity = 1
        }
        currentIndex = endIndex
        // 如果还有未处理的内容，使用 requestAnimationFrame 继续处理
        if (currentIndex < articleBodies.length) {
          requestAnimationFrame(processBatch)
        } else {
          // 所有内容处理完成后优化侧边栏
          optimizeSidebar()
        }
      }
      // 开始分批处理
      processBatch()
    }

    // 重试机制，最多重试 3 次，每次间隔 100ms
    const tryProcessContent = (retryCount = 0) => {
      const articleBodies = document.querySelectorAll('.article-body-inner')
      if (articleBodies.length > 0) {
        processMainContent()
      } else if (retryCount < 3) {
        setTimeout(() => tryProcessContent(retryCount + 1), 100)
      }
    }
    tryProcessContent()
  }

  /**
   * 初始化脚本，启动主要功能和创建控制界面
   */
  function initializeScript() {
    main()
    createUnifiedSettingsPanel()
    initializeFuriganaDisplay()
  }

  // 根据 DOM 加载状态选择合适的启动时机
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeScript, { once: true })
  } else {
    // DOM 已经加载完成，直接执行
    initializeScript()
  }
})()
